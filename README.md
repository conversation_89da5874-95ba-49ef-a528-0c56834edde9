# arzach

A new Flutter project.

## Getting Started

# أرزاخ - تطبيق دردشة عربي

تطبيق دردشة آمن وسريع يدعم اللغة العربية مع واجهة تسجيل دخول عبر رقم الهاتف وإعدادات متقدمة مشابهة لتطبيق Signal.

## الميزات

### 🔐 المصادقة والأمان
- تسجيل الدخول عبر رقم الهاتف (مثل WhatsApp)
- التحقق من الهوية عبر رمز OTP
- تشفير المحادثات من طرف إلى طرف

### 💬 المحادثات
- دردشة فردية سريعة وآمنة
- دعم كامل للغة العربية واتجاه RTL
- إرسال النصوص والصور والملفات
- إيصالات القراءة
- حالة الاتصال (متصل/غير متصل)

### ⚙️ الإعدادات (مثل Signal)
- إعدادات الملف الشخصي
- إعدادات الخصوصية والأمان
- إعدادات الإشعارات
- إعدادات التخزين والبيانات

### 🎨 التصميم
- واجهة مستخدم عربية أنيقة
- دعم كامل لاتجاه RTL
- تصميم متجاوب لجميع الأجهزة
- ألوان وخطوط مناسبة للمحتوى العربي

## متطلبات التشغيل

- Flutter 3.7.2 أو أحدث
- Dart 3.0 أو أحدث
- حساب Firebase مع تفعيل:
  - Authentication (Phone)
  - Firestore Database
  - Storage

## إعداد المشروع

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd arzach
```

### 2. تثبيت التبعيات
```bash
flutter pub get
```

### 3. إعداد Firebase

#### أ. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد
3. فعّل Authentication وحدد Phone كطريقة تسجيل دخول
4. أنشئ قاعدة بيانات Firestore
5. فعّل Storage

#### ب. إضافة التطبيق لـ Firebase
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تثبيت FlutterFire CLI
dart pub global activate flutterfire_cli

# إعداد Firebase للمشروع
flutterfire configure
```

#### ج. تحديث إعدادات Firebase
بعد تشغيل `flutterfire configure`، سيتم إنشاء ملف `firebase_options.dart` تلقائياً مع الإعدادات الصحيحة.

### 4. إعداد قواعد Firestore

أضف هذه القواعد في Firebase Console > Firestore Database > Rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null;
    }

    // قواعد المحادثات
    match /chats/{chatId} {
      allow read, write: if request.auth != null &&
        request.auth.uid in resource.data.participants;

      // قواعد الرسائل
      match /messages/{messageId} {
        allow read, write: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
      }
    }
  }
}
```

### 5. إعداد قواعد Storage

أضف هذه القواعد في Firebase Console > Storage > Rules:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    match /chats/{chatId}/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## تشغيل التطبيق

### للتطوير
```bash
flutter run
```

### للإنتاج
```bash
# Android
flutter build apk --release

# iOS
flutter build ios --release
```

## هيكل المشروع

```
lib/
├── models/           # نماذج البيانات
├── services/         # خدمات Firebase والمصادقة
├── screens/          # شاشات التطبيق
│   ├── auth/        # شاشات المصادقة
│   ├── home/        # الشاشة الرئيسية
│   ├── chat/        # شاشات الدردشة
│   ├── contacts/    # شاشة جهات الاتصال
│   └── settings/    # شاشات الإعدادات
├── widgets/          # مكونات واجهة المستخدم
└── main.dart        # نقطة البداية
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التغييرات مع الاختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى فتح issue في GitHub.
