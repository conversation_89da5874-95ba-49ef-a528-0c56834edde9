import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/message_model.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';

class ChatService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String? get currentUserId => _auth.currentUser?.uid;

  // إنشاء أو الحصول على محادثة
  Future<String> createOrGetChat(String otherUserId) async {
    if (currentUserId == null) throw Exception('المستخدم غير مسجل الدخول');

    final participants = [currentUserId!, otherUserId]..sort();
    final chatId = participants.join('_');

    final chatDoc = await _firestore.collection('chats').doc(chatId).get();
    
    if (!chatDoc.exists) {
      final newChat = ChatModel(
        chatId: chatId,
        participants: participants,
        lastActivity: DateTime.now(),
        unreadCount: {currentUserId!: 0, otherUserId: 0},
      );
      
      await _firestore.collection('chats').doc(chatId).set(newChat.toMap());
    }
    
    return chatId;
  }

  // إرسال رسالة
  Future<void> sendMessage({
    required String chatId,
    required String receiverId,
    required String content,
    required MessageType type,
    String? mediaUrl,
    String? fileName,
    int? fileSize,
    String? replyToMessageId,
  }) async {
    if (currentUserId == null) throw Exception('المستخدم غير مسجل الدخول');

    final messageId = _firestore.collection('chats').doc(chatId).collection('messages').doc().id;
    
    final message = MessageModel(
      messageId: messageId,
      senderId: currentUserId!,
      receiverId: receiverId,
      content: content,
      type: type,
      timestamp: DateTime.now(),
      mediaUrl: mediaUrl,
      fileName: fileName,
      fileSize: fileSize,
      replyToMessageId: replyToMessageId,
    );

    // إضافة الرسالة إلى مجموعة الرسائل
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .set(message.toMap());

    // تحديث آخر رسالة في المحادثة
    await _firestore.collection('chats').doc(chatId).update({
      'lastMessage': message.toMap(),
      'lastActivity': DateTime.now().millisecondsSinceEpoch,
      'unreadCount.$receiverId': FieldValue.increment(1),
    });
  }

  // الحصول على الرسائل
  Stream<List<MessageModel>> getMessages(String chatId) {
    return _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MessageModel.fromMap(doc.data()))
            .toList());
  }

  // الحصول على المحادثات
  Stream<List<ChatModel>> getChats() {
    if (currentUserId == null) return Stream.value([]);
    
    return _firestore
        .collection('chats')
        .where('participants', arrayContains: currentUserId)
        .orderBy('lastActivity', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ChatModel.fromMap(doc.data()))
            .toList());
  }

  // تحديد الرسائل كمقروءة
  Future<void> markMessagesAsRead(String chatId) async {
    if (currentUserId == null) return;

    final batch = _firestore.batch();
    
    // تحديث عداد الرسائل غير المقروءة
    final chatRef = _firestore.collection('chats').doc(chatId);
    batch.update(chatRef, {'unreadCount.$currentUserId': 0});

    // تحديث الرسائل غير المقروءة
    final unreadMessages = await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .where('receiverId', isEqualTo: currentUserId)
        .where('isRead', isEqualTo: false)
        .get();

    for (final doc in unreadMessages.docs) {
      batch.update(doc.reference, {'isRead': true});
    }

    await batch.commit();
  }

  // البحث عن المستخدمين
  Future<List<UserModel>> searchUsers(String query) async {
    if (query.isEmpty) return [];

    final snapshot = await _firestore
        .collection('users')
        .where('phoneNumber', isGreaterThanOrEqualTo: query)
        .where('phoneNumber', isLessThanOrEqualTo: '$query\uf8ff')
        .limit(20)
        .get();

    return snapshot.docs
        .map((doc) => UserModel.fromMap(doc.data()))
        .where((user) => user.uid != currentUserId)
        .toList();
  }

  // الحصول على بيانات المستخدم
  Future<UserModel?> getUserData(String userId) async {
    final doc = await _firestore.collection('users').doc(userId).get();
    if (doc.exists) {
      return UserModel.fromMap(doc.data()!);
    }
    return null;
  }

  // حذف رسالة
  Future<void> deleteMessage(String chatId, String messageId) async {
    await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId)
        .delete();
  }
}
