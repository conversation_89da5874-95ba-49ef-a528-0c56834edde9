import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _messageNotifications = true;
  bool _groupNotifications = true;
  bool _callNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _showPreview = true;
  String _notificationTone = 'افتراضي';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _messageNotifications = prefs.getBool('message_notifications') ?? true;
      _groupNotifications = prefs.getBool('group_notifications') ?? true;
      _callNotifications = prefs.getBool('call_notifications') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
      _showPreview = prefs.getBool('show_preview') ?? true;
      _notificationTone = prefs.getString('notification_tone') ?? 'افتراضي';
    });
  }

  void _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('message_notifications', _messageNotifications);
    await prefs.setBool('group_notifications', _groupNotifications);
    await prefs.setBool('call_notifications', _callNotifications);
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('vibration_enabled', _vibrationEnabled);
    await prefs.setBool('show_preview', _showPreview);
    await prefs.setString('notification_tone', _notificationTone);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            title: 'أنواع الإشعارات',
            children: [
              _buildSwitchTile(
                title: 'إشعارات الرسائل',
                subtitle: 'تلقي إشعارات للرسائل الجديدة',
                value: _messageNotifications,
                onChanged: (value) {
                  setState(() {
                    _messageNotifications = value;
                  });
                  _saveSettings();
                },
              ),
              _buildSwitchTile(
                title: 'إشعارات المجموعات',
                subtitle: 'تلقي إشعارات لرسائل المجموعات',
                value: _groupNotifications,
                onChanged: (value) {
                  setState(() {
                    _groupNotifications = value;
                  });
                  _saveSettings();
                },
              ),
              _buildSwitchTile(
                title: 'إشعارات المكالمات',
                subtitle: 'تلقي إشعارات للمكالمات الواردة',
                value: _callNotifications,
                onChanged: (value) {
                  setState(() {
                    _callNotifications = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          _buildSection(
            title: 'إعدادات الصوت والاهتزاز',
            children: [
              _buildSwitchTile(
                title: 'الصوت',
                subtitle: 'تشغيل صوت الإشعارات',
                value: _soundEnabled,
                onChanged: (value) {
                  setState(() {
                    _soundEnabled = value;
                  });
                  _saveSettings();
                },
              ),
              _buildActionTile(
                title: 'نغمة الإشعار',
                subtitle: _notificationTone,
                icon: Icons.music_note,
                onTap: _selectNotificationTone,
              ),
              _buildSwitchTile(
                title: 'الاهتزاز',
                subtitle: 'تفعيل الاهتزاز مع الإشعارات',
                value: _vibrationEnabled,
                onChanged: (value) {
                  setState(() {
                    _vibrationEnabled = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          _buildSection(
            title: 'إعدادات العرض',
            children: [
              _buildSwitchTile(
                title: 'معاينة الرسائل',
                subtitle: 'إظهار محتوى الرسالة في الإشعار',
                value: _showPreview,
                onChanged: (value) {
                  setState(() {
                    _showPreview = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          _buildSection(
            title: 'إعدادات متقدمة',
            children: [
              _buildActionTile(
                title: 'عدم الإزعاج',
                subtitle: 'تعيين أوقات عدم تلقي الإشعارات',
                icon: Icons.do_not_disturb,
                onTap: () {
                  // شاشة إعدادات عدم الإزعاج
                },
              ),
              _buildActionTile(
                title: 'إشعارات مخصصة',
                subtitle: 'تخصيص الإشعارات لجهات اتصال معينة',
                icon: Icons.person,
                onTap: () {
                  // شاشة الإشعارات المخصصة
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.blue.shade600,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Colors.blue.shade600,
        size: 28,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey.shade400,
        size: 16,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  void _selectNotificationTone() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر نغمة الإشعار'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildToneOption('افتراضي'),
            _buildToneOption('نغمة 1'),
            _buildToneOption('نغمة 2'),
            _buildToneOption('نغمة 3'),
            _buildToneOption('صامت'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildToneOption(String tone) {
    return RadioListTile<String>(
      title: Text(tone),
      value: tone,
      groupValue: _notificationTone,
      onChanged: (value) {
        setState(() {
          _notificationTone = value!;
        });
        _saveSettings();
        Navigator.pop(context);
      },
      activeColor: Colors.blue.shade600,
    );
  }
}
