import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  bool _showLastSeen = true;
  bool _showProfilePhoto = true;
  bool _showStatus = true;
  bool _readReceipts = true;
  bool _allowGroupInvites = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _showLastSeen = prefs.getBool('show_last_seen') ?? true;
      _showProfilePhoto = prefs.getBool('show_profile_photo') ?? true;
      _showStatus = prefs.getBool('show_status') ?? true;
      _readReceipts = prefs.getBool('read_receipts') ?? true;
      _allowGroupInvites = prefs.getBool('allow_group_invites') ?? true;
    });
  }

  void _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('show_last_seen', _showLastSeen);
    await prefs.setBool('show_profile_photo', _showProfilePhoto);
    await prefs.setBool('show_status', _showStatus);
    await prefs.setBool('read_receipts', _readReceipts);
    await prefs.setBool('allow_group_invites', _allowGroupInvites);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الخصوصية والأمان'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            title: 'من يمكنه رؤية معلوماتي',
            children: [
              _buildSwitchTile(
                title: 'آخر ظهور',
                subtitle: 'السماح للآخرين برؤية آخر ظهور لك',
                value: _showLastSeen,
                onChanged: (value) {
                  setState(() {
                    _showLastSeen = value;
                  });
                  _saveSettings();
                },
              ),
              _buildSwitchTile(
                title: 'صورة الملف الشخصي',
                subtitle: 'السماح للآخرين برؤية صورتك الشخصية',
                value: _showProfilePhoto,
                onChanged: (value) {
                  setState(() {
                    _showProfilePhoto = value;
                  });
                  _saveSettings();
                },
              ),
              _buildSwitchTile(
                title: 'الحالة',
                subtitle: 'السماح للآخرين برؤية حالتك',
                value: _showStatus,
                onChanged: (value) {
                  setState(() {
                    _showStatus = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          _buildSection(
            title: 'الرسائل',
            children: [
              _buildSwitchTile(
                title: 'إيصالات القراءة',
                subtitle: 'إظهار علامة القراءة للآخرين',
                value: _readReceipts,
                onChanged: (value) {
                  setState(() {
                    _readReceipts = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          _buildSection(
            title: 'المجموعات',
            children: [
              _buildSwitchTile(
                title: 'دعوات المجموعات',
                subtitle: 'السماح للآخرين بإضافتك للمجموعات',
                value: _allowGroupInvites,
                onChanged: (value) {
                  setState(() {
                    _allowGroupInvites = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          _buildSection(
            title: 'الأمان',
            children: [
              _buildActionTile(
                title: 'المحادثات المحظورة',
                subtitle: 'إدارة المستخدمين المحظورين',
                icon: Icons.block,
                onTap: () {
                  // شاشة المحادثات المحظورة
                },
              ),
              _buildActionTile(
                title: 'تشفير المحادثات',
                subtitle: 'معلومات حول تشفير الرسائل',
                icon: Icons.security,
                onTap: () {
                  _showEncryptionInfo();
                },
              ),
              _buildActionTile(
                title: 'تقرير مشكلة',
                subtitle: 'الإبلاغ عن مشكلة أو سوء استخدام',
                icon: Icons.report,
                onTap: () {
                  // شاشة تقرير المشاكل
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.blue.shade600,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Colors.blue.shade600,
        size: 28,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey.shade400,
        size: 16,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  void _showEncryptionInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تشفير المحادثات'),
        content: const Text(
          'جميع رسائلك محمية بتشفير من طرف إلى طرف. هذا يعني أن رسائلك آمنة ولا يمكن لأحد قراءتها سوى المرسل والمستقبل.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
