import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../services/auth_service.dart';

class OTPVerificationScreen extends StatefulWidget {
  final String verificationId;
  final String phoneNumber;

  const OTPVerificationScreen({
    super.key,
    required this.verificationId,
    required this.phoneNumber,
  });

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen> {
  final TextEditingController _otpController = TextEditingController();
  final AuthService _authService = AuthService();
  bool _isLoading = false;
  bool _isResending = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 32),
              
              // أيقونة التحقق
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.sms_outlined,
                  size: 50,
                  color: Colors.blue.shade600,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // عنوان التحقق
              const Text(
                'تحقق من رقم هاتفك',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // نص توضيحي
              Text(
                'أدخل الرمز المرسل إلى\n${widget.phoneNumber}',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 48),
              
              // حقول إدخال رمز OTP
              Directionality(
                textDirection: TextDirection.ltr,
                child: PinCodeTextField(
                  appContext: context,
                  length: 6,
                  controller: _otpController,
                  keyboardType: TextInputType.number,
                  animationType: AnimationType.fade,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(12),
                    fieldHeight: 60,
                    fieldWidth: 50,
                    activeFillColor: Colors.blue.shade50,
                    inactiveFillColor: Colors.grey.shade100,
                    selectedFillColor: Colors.blue.shade100,
                    activeColor: Colors.blue.shade600,
                    inactiveColor: Colors.grey.shade300,
                    selectedColor: Colors.blue.shade600,
                  ),
                  enableActiveFill: true,
                  onCompleted: (code) {
                    _verifyOTP(code);
                  },
                  onChanged: (value) {},
                ),
              ),
              
              const SizedBox(height: 32),
              
              // زر التحقق
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : () => _verifyOTP(_otpController.text),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                          'تحقق',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // زر إعادة الإرسال
              TextButton(
                onPressed: _isResending ? null : _resendOTP,
                child: _isResending
                    ? const CircularProgressIndicator()
                    : const Text(
                        'لم تستلم الرمز؟ إعادة الإرسال',
                        style: TextStyle(
                          color: Colors.blue,
                          fontSize: 14,
                        ),
                      ),
              ),
              
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  void _verifyOTP(String otp) async {
    if (otp.length != 6) {
      _showSnackBar('يرجى إدخال رمز التحقق كاملاً');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userCredential = await _authService.signInWithPhoneNumber(
        verificationId: widget.verificationId,
        smsCode: otp,
      );

      if (userCredential != null && mounted) {
        // الانتقال إلى الشاشة الرئيسية
        Navigator.pushNamedAndRemoveUntil(
          context,
          '/home',
          (route) => false,
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('رمز التحقق غير صحيح');
    }
  }

  void _resendOTP() async {
    setState(() {
      _isResending = true;
    });

    try {
      await _authService.verifyPhoneNumber(
        phoneNumber: widget.phoneNumber,
        verificationCompleted: (credential) {},
        verificationFailed: (e) {
          setState(() {
            _isResending = false;
          });
          _showSnackBar('فشل في إعادة إرسال الرمز');
        },
        codeSent: (verificationId, resendToken) {
          setState(() {
            _isResending = false;
          });
          _showSnackBar('تم إرسال رمز جديد');
        },
        codeAutoRetrievalTimeout: (verificationId) {},
      );
    } catch (e) {
      setState(() {
        _isResending = false;
      });
      _showSnackBar('حدث خطأ في إعادة الإرسال');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: message.contains('تم') ? Colors.green : Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }
}
