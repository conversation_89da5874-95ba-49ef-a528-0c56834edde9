import 'message_model.dart';

class ChatModel {
  final String chatId;
  final List<String> participants;
  final MessageModel? lastMessage;
  final DateTime lastActivity;
  final Map<String, int> unreadCount;
  final bool isGroup;
  final String? groupName;
  final String? groupPhotoUrl;
  final String? groupDescription;
  final List<String>? groupAdmins;

  ChatModel({
    required this.chatId,
    required this.participants,
    this.lastMessage,
    required this.lastActivity,
    required this.unreadCount,
    this.isGroup = false,
    this.groupName,
    this.groupPhotoUrl,
    this.groupDescription,
    this.groupAdmins,
  });

  Map<String, dynamic> toMap() {
    return {
      'chatId': chatId,
      'participants': participants,
      'lastMessage': lastMessage?.toMap(),
      'lastActivity': lastActivity.millisecondsSinceEpoch,
      'unreadCount': unreadCount,
      'isGroup': isGroup,
      'groupName': groupName,
      'groupPhotoUrl': groupPhotoUrl,
      'groupDescription': groupDescription,
      'groupAdmins': groupAdmins,
    };
  }

  factory ChatModel.fromMap(Map<String, dynamic> map) {
    return ChatModel(
      chatId: map['chatId'] ?? '',
      participants: List<String>.from(map['participants'] ?? []),
      lastMessage: map['lastMessage'] != null 
          ? MessageModel.fromMap(map['lastMessage'])
          : null,
      lastActivity: DateTime.fromMillisecondsSinceEpoch(map['lastActivity'] ?? 0),
      unreadCount: Map<String, int>.from(map['unreadCount'] ?? {}),
      isGroup: map['isGroup'] ?? false,
      groupName: map['groupName'],
      groupPhotoUrl: map['groupPhotoUrl'],
      groupDescription: map['groupDescription'],
      groupAdmins: map['groupAdmins'] != null 
          ? List<String>.from(map['groupAdmins'])
          : null,
    );
  }

  ChatModel copyWith({
    String? chatId,
    List<String>? participants,
    MessageModel? lastMessage,
    DateTime? lastActivity,
    Map<String, int>? unreadCount,
    bool? isGroup,
    String? groupName,
    String? groupPhotoUrl,
    String? groupDescription,
    List<String>? groupAdmins,
  }) {
    return ChatModel(
      chatId: chatId ?? this.chatId,
      participants: participants ?? this.participants,
      lastMessage: lastMessage ?? this.lastMessage,
      lastActivity: lastActivity ?? this.lastActivity,
      unreadCount: unreadCount ?? this.unreadCount,
      isGroup: isGroup ?? this.isGroup,
      groupName: groupName ?? this.groupName,
      groupPhotoUrl: groupPhotoUrl ?? this.groupPhotoUrl,
      groupDescription: groupDescription ?? this.groupDescription,
      groupAdmins: groupAdmins ?? this.groupAdmins,
    );
  }

  String getOtherParticipant(String currentUserId) {
    return participants.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
  }
}
