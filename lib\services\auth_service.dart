import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? get currentUser => _auth.currentUser;
  String? get currentUserId => _auth.currentUser?.uid;

  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // إرسال رمز التحقق إلى رقم الهاتف
  Future<void> verifyPhoneNumber({
    required String phoneNumber,
    required Function(PhoneAuthCredential) verificationCompleted,
    required Function(FirebaseAuthException) verificationFailed,
    required Function(String, int?) codeSent,
    required Function(String) codeAutoRetrievalTimeout,
  }) async {
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: verificationCompleted,
      verificationFailed: verificationFailed,
      codeSent: codeSent,
      codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
      timeout: const Duration(seconds: 60),
    );
  }

  // تسجيل الدخول باستخدام رمز التحقق
  Future<UserCredential?> signInWithPhoneNumber({
    required String verificationId,
    required String smsCode,
  }) async {
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: smsCode,
      );
      
      final userCredential = await _auth.signInWithCredential(credential);
      
      // إنشاء أو تحديث بيانات المستخدم في Firestore
      if (userCredential.user != null) {
        await _createOrUpdateUser(userCredential.user!);
      }
      
      return userCredential;
    } catch (e) {
      rethrow;
    }
  }

  // إنشاء أو تحديث بيانات المستخدم
  Future<void> _createOrUpdateUser(User user) async {
    final userDoc = await _firestore.collection('users').doc(user.uid).get();
    
    if (!userDoc.exists) {
      // إنشاء مستخدم جديد
      final newUser = UserModel(
        uid: user.uid,
        phoneNumber: user.phoneNumber ?? '',
        displayName: user.displayName,
        photoURL: user.photoURL,
        lastSeen: DateTime.now(),
        isOnline: true,
        createdAt: DateTime.now(),
      );
      
      await _firestore.collection('users').doc(user.uid).set(newUser.toMap());
    } else {
      // تحديث حالة المستخدم
      await _firestore.collection('users').doc(user.uid).update({
        'isOnline': true,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
      });
    }
  }

  // الحصول على بيانات المستخدم الحالي
  Future<UserModel?> getCurrentUserData() async {
    if (currentUser == null) return null;
    
    final doc = await _firestore.collection('users').doc(currentUser!.uid).get();
    if (doc.exists) {
      return UserModel.fromMap(doc.data()!);
    }
    return null;
  }

  // تحديث بيانات المستخدم
  Future<void> updateUserData({
    String? displayName,
    String? photoURL,
  }) async {
    if (currentUser == null) return;
    
    final updates = <String, dynamic>{};
    if (displayName != null) updates['displayName'] = displayName;
    if (photoURL != null) updates['photoURL'] = photoURL;
    
    if (updates.isNotEmpty) {
      await _firestore.collection('users').doc(currentUser!.uid).update(updates);
    }
  }

  // تحديث حالة الاتصال
  Future<void> updateOnlineStatus(bool isOnline) async {
    if (currentUser == null) return;
    
    await _firestore.collection('users').doc(currentUser!.uid).update({
      'isOnline': isOnline,
      'lastSeen': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // تسجيل الخروج
  Future<void> signOut() async {
    await updateOnlineStatus(false);
    await _auth.signOut();
  }
}
