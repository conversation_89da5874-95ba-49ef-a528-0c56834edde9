import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';
import '../services/chat_service.dart';
import '../services/auth_service.dart';

class ChatTile extends StatefulWidget {
  final ChatModel chat;
  final VoidCallback onTap;

  const ChatTile({
    super.key,
    required this.chat,
    required this.onTap,
  });

  @override
  State<ChatTile> createState() => _ChatTileState();
}

class _ChatTileState extends State<ChatTile> {
  final ChatService _chatService = ChatService();
  final AuthService _authService = AuthService();
  UserModel? _otherUser;

  @override
  void initState() {
    super.initState();
    _loadOtherUser();
  }

  void _loadOtherUser() async {
    final currentUserId = _authService.currentUserId;
    if (currentUserId == null) return;

    final otherUserId = widget.chat.getOtherParticipant(currentUserId);
    final user = await _chatService.getUserData(otherUserId);
    
    if (mounted) {
      setState(() {
        _otherUser = user;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_otherUser == null) {
      return const ListTile(
        leading: CircleAvatar(
          child: CircularProgressIndicator(),
        ),
        title: Text('جاري التحميل...'),
      );
    }

    final currentUserId = _authService.currentUserId ?? '';
    final unreadCount = widget.chat.unreadCount[currentUserId] ?? 0;
    final lastMessage = widget.chat.lastMessage;

    return ListTile(
      onTap: widget.onTap,
      leading: Stack(
        children: [
          CircleAvatar(
            radius: 28,
            backgroundColor: Colors.grey.shade300,
            backgroundImage: _otherUser!.photoURL != null
                ? NetworkImage(_otherUser!.photoURL!)
                : null,
            child: _otherUser!.photoURL == null
                ? Text(
                    _getInitials(_otherUser!.displayName ?? _otherUser!.phoneNumber),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )
                : null,
          ),
          if (_otherUser!.isOnline)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              _otherUser!.displayName ?? _otherUser!.phoneNumber,
              style: TextStyle(
                fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                fontSize: 16,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (lastMessage != null)
            Text(
              _formatTime(widget.chat.lastActivity),
              style: TextStyle(
                fontSize: 12,
                color: unreadCount > 0 ? Colors.blue.shade600 : Colors.grey,
                fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
              ),
            ),
        ],
      ),
      subtitle: Row(
        children: [
          if (lastMessage != null) ...[
            if (lastMessage.senderId == currentUserId)
              Icon(
                lastMessage.isRead ? Icons.done_all : Icons.done,
                size: 16,
                color: lastMessage.isRead ? Colors.blue : Colors.grey,
              ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                _getMessagePreview(lastMessage),
                style: TextStyle(
                  color: unreadCount > 0 ? Colors.black87 : Colors.grey,
                  fontWeight: unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ] else
            const Expanded(
              child: Text(
                'لا توجد رسائل',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          if (unreadCount > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.shade600,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                unreadCount > 99 ? '99+' : unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '؟';
    
    final words = name.trim().split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else {
      return name[0].toUpperCase();
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      // اليوم
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays == 1) {
      // أمس
      return 'أمس';
    } else if (difference.inDays < 7) {
      // هذا الأسبوع
      return DateFormat('EEEE', 'ar').format(dateTime);
    } else {
      // أكثر من أسبوع
      return DateFormat('dd/MM/yyyy').format(dateTime);
    }
  }

  String _getMessagePreview(dynamic lastMessage) {
    if (lastMessage == null) return '';
    
    switch (lastMessage.type.name) {
      case 'text':
        return lastMessage.content;
      case 'image':
        return '📷 صورة';
      case 'audio':
        return '🎵 رسالة صوتية';
      case 'video':
        return '🎥 فيديو';
      case 'file':
        return '📎 ملف';
      default:
        return lastMessage.content;
    }
  }
}
