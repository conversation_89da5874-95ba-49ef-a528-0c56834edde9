class UserModel {
  final String uid;
  final String phoneNumber;
  final String? displayName;
  final String? photoURL;
  final DateTime lastSeen;
  final bool isOnline;
  final DateTime createdAt;

  UserModel({
    required this.uid,
    required this.phoneNumber,
    this.displayName,
    this.photoURL,
    required this.lastSeen,
    required this.isOnline,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'phoneNumber': phoneNumber,
      'displayName': displayName,
      'photoURL': photoURL,
      'lastSeen': lastSeen.millisecondsSinceEpoch,
      'isOnline': isOnline,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      displayName: map['displayName'],
      photoURL: map['photoURL'],
      lastSeen: DateTime.fromMillisecondsSinceEpoch(map['lastSeen'] ?? 0),
      isOnline: map['isOnline'] ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  UserModel copyWith({
    String? uid,
    String? phoneNumber,
    String? displayName,
    String? photoURL,
    DateTime? lastSeen,
    bool? isOnline,
    DateTime? createdAt,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      lastSeen: lastSeen ?? this.lastSeen,
      isOnline: isOnline ?? this.isOnline,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
